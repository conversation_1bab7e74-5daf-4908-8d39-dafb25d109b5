// Dart imports
import 'package:flutter/material.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports - Models
import 'package:culture_connect/models/travel/travel.dart';

// Project imports - Providers
import 'package:culture_connect/providers/travel/travel_services_provider.dart';

// Project imports - Screens
import 'package:culture_connect/screens/travel/car_rental/car_rental_details_screen.dart';
import 'package:culture_connect/screens/travel/car_rental/car_rental_list_screen_enhanced.dart';
import 'package:culture_connect/screens/travel/cruise/cruise_details_screen.dart';
import 'package:culture_connect/screens/travel/cruise/cruise_list_screen.dart';
import 'package:culture_connect/screens/travel/flight/flight_details_screen.dart';
import 'package:culture_connect/screens/travel/flight/flight_list_screen.dart';
import 'package:culture_connect/screens/travel/hotel/hotel_details_screen_enhanced.dart';
import 'package:culture_connect/screens/travel/hotel/hotel_list_screen_enhanced.dart';
import 'package:culture_connect/screens/travel/insurance/insurance_home_screen.dart';
import 'package:culture_connect/screens/travel/loyalty/loyalty_program_screen.dart';
import 'package:culture_connect/screens/travel/private_security/private_security_details_screen.dart';
import 'package:culture_connect/screens/travel/private_security/private_security_list_screen.dart';
import 'package:culture_connect/screens/travel/restaurant/restaurant_details_screen.dart';
import 'package:culture_connect/screens/travel/restaurant/restaurant_list_screen.dart';
import 'package:culture_connect/screens/travel/visa/visa_requirements_screen.dart';

// Project imports - Widgets
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/travel/featured_travel_service_card.dart';
import 'package:culture_connect/widgets/travel/travel_service_card.dart';
import 'package:culture_connect/widgets/travel/travel_service_category_card.dart';

/// Screen for displaying travel services
class TravelServicesScreen extends ConsumerStatefulWidget {
  /// Creates a new travel services screen
  const TravelServicesScreen({super.key});

  @override
  ConsumerState<TravelServicesScreen> createState() =>
      _TravelServicesScreenState();
}

class _TravelServicesScreenState extends ConsumerState<TravelServicesScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: NestedScrollView(
        controller: _scrollController,
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              title: _isSearching
                  ? TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Search travel services...',
                        border: InputBorder.none,
                        hintStyle: TextStyle(
                            color: theme.colorScheme.onSurface.withAlpha(153)),
                      ),
                      style: TextStyle(color: theme.colorScheme.onSurface),
                      autofocus: true,
                      onChanged: (value) {
                        // Implement search functionality
                      },
                    )
                  : const Text('Travel Services'),
              actions: [
                IconButton(
                  icon: Icon(_isSearching ? Icons.close : Icons.search),
                  onPressed: () {
                    setState(() {
                      _isSearching = !_isSearching;
                      if (!_isSearching) {
                        _searchController.clear();
                      }
                    });
                  },
                ),
              ],
              floating: true,
              pinned: true,
              snap: true,
              bottom: TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(text: 'Explore'),
                  Tab(text: 'Categories'),
                ],
                labelColor: theme.colorScheme.primary,
                unselectedLabelColor: theme.colorScheme.onSurface,
                indicatorColor: theme.colorScheme.primary,
              ),
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildExploreTab(),
            _buildCategoriesTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildExploreTab() {
    return RefreshIndicator(
      onRefresh: () async {
        // Refresh data
        await Future.wait([
          ref.refresh(featuredTravelServicesProvider.future),
          ref.refresh(onSaleTravelServicesProvider.future),
        ]);
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.symmetric(vertical: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Featured Travel Services
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Featured',
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ),
            SizedBox(height: 8),
            SizedBox(
              height: 220,
              child: _buildFeaturedTravelServices(),
            ),

            SizedBox(height: 24),

            // On Sale Travel Services
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'On Sale',
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ),
            SizedBox(height: 8),
            _buildOnSaleTravelServices(),

            SizedBox(height: 24),

            // Loyalty Programs
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Loyalty Programs',
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ),
            SizedBox(height: 8),
            _buildLoyaltyPrograms(),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturedTravelServices() {
    final featuredTravelServicesAsyncValue =
        ref.watch(featuredTravelServicesProvider);

    return featuredTravelServicesAsyncValue.when(
      data: (featuredTravelServices) {
        if (featuredTravelServices.isEmpty) {
          return const Center(
            child: Text('No featured travel services available'),
          );
        }

        return ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: EdgeInsets.symmetric(horizontal: 16),
          itemCount: featuredTravelServices.length,
          itemBuilder: (context, index) {
            final travelService = featuredTravelServices[index];
            return Padding(
              padding: EdgeInsets.only(right: 16),
              child: FeaturedTravelServiceCard(
                travelService: travelService,
                onTap: () => _navigateToTravelServiceDetails(travelService),
              ),
            );
          },
        );
      },
      loading: () => const Center(child: LoadingIndicator()),
      error: (error, stackTrace) => Center(
        child: ErrorView(
          error: error.toString(),
          onRetry: () => ref.refresh(featuredTravelServicesProvider),
        ),
      ),
    );
  }

  Widget _buildOnSaleTravelServices() {
    final onSaleTravelServicesAsyncValue =
        ref.watch(onSaleTravelServicesProvider);

    return onSaleTravelServicesAsyncValue.when(
      data: (onSaleTravelServices) {
        if (onSaleTravelServices.isEmpty) {
          return const Center(
            child: Text('No travel services on sale'),
          );
        }

        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: EdgeInsets.symmetric(horizontal: 16),
          itemCount: onSaleTravelServices.length,
          itemBuilder: (context, index) {
            final travelService = onSaleTravelServices[index];
            return Padding(
              padding: EdgeInsets.only(bottom: 16),
              child: TravelServiceCard(
                travelService: travelService,
                onTap: () => _navigateToTravelServiceDetails(travelService),
              ),
            );
          },
        );
      },
      loading: () => const Center(child: LoadingIndicator()),
      error: (error, stackTrace) => Center(
        child: ErrorView(
          error: error.toString(),
          onRetry: () => ref.refresh(onSaleTravelServicesProvider),
        ),
      ),
    );
  }

  Widget _buildLoyaltyPrograms() {
    final loyaltyProgramsAsyncValue = ref.watch(loyaltyProgramsProvider);

    return loyaltyProgramsAsyncValue.when(
      data: (loyaltyPrograms) {
        if (loyaltyPrograms.isEmpty) {
          return const Center(
            child: Text('No loyalty programs available'),
          );
        }

        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: EdgeInsets.symmetric(horizontal: 16),
          itemCount: loyaltyPrograms.length,
          itemBuilder: (context, index) {
            final loyaltyProgram = loyaltyPrograms[index];
            return Padding(
              padding: EdgeInsets.only(bottom: 16),
              child: Card(
                child: InkWell(
                  onTap: () => _navigateToLoyaltyProgramDetails(loyaltyProgram),
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            CircleAvatar(
                              backgroundImage:
                                  NetworkImage(loyaltyProgram.companyLogoUrl),
                              radius: 24,
                            ),
                            SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    loyaltyProgram.name,
                                    style:
                                        Theme.of(context).textTheme.titleMedium,
                                  ),
                                  Text(
                                    loyaltyProgram.companyName,
                                    style:
                                        Theme.of(context).textTheme.bodyMedium,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 16),
                        Text(
                          '${loyaltyProgram.currentPoints} points',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        SizedBox(height: 8),
                        if (loyaltyProgram.nextTier != null) ...[
                          Text(
                            loyaltyProgram.formattedProgressToNextTier,
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                          SizedBox(height: 4),
                          LinearProgressIndicator(
                            value: loyaltyProgram.progressToNextTier,
                            backgroundColor: Theme.of(context)
                                .colorScheme
                                .surfaceContainerHighest,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              loyaltyProgram.nextTier!.color,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
      loading: () => const Center(child: LoadingIndicator()),
      error: (error, stackTrace) => Center(
        child: ErrorView(
          error: error.toString(),
          onRetry: () => ref.refresh(loyaltyProgramsProvider),
        ),
      ),
    );
  }

  Widget _buildCategoriesTab() {
    return GridView.count(
      crossAxisCount: 2,
      padding: EdgeInsets.all(16),
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      children: [
        // Standard travel service categories
        ...TravelServiceType.values.map((type) {
          return TravelServiceCategoryCard(
            type: type,
            onTap: () => _navigateToTravelServiceCategory(type),
          );
        }),

        // Additional services
        Card(
          child: InkWell(
            onTap: () => _navigateToInsurance(),
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.health_and_safety,
                    size: 48,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Travel Insurance',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _navigateToTravelServiceDetails(TravelService travelService) {
    // Navigate to the appropriate details screen based on the travel service type
    if (travelService is CarRental) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) =>
              CarRentalDetailsScreen(carRental: travelService),
        ),
      );
    } else if (travelService is PrivateSecurity) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) =>
              PrivateSecurityDetailsScreen(privateSecurity: travelService),
        ),
      );
    } else if (travelService is Hotel) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) =>
              HotelDetailsScreenEnhanced(thisotel: travelService),
        ),
      );
    } else if (travelService is Restaurant) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) =>
              RestaurantDetailsScreen(thisestaurant: travelService),
        ),
      );
    } else if (travelService is Flight) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => FlightDetailsScreen(flight: travelService),
        ),
      );
    } else if (travelService is Cruise) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CruiseDetailsScreen(cruise: travelService),
        ),
      );
    }
  }

  void _navigateToTravelServiceCategory(TravelServiceType type) {
    // Navigate to the appropriate list screen based on the travel service type
    switch (type) {
      case TravelServiceType.carRental:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const CarRentalListScreenEnhanced(),
          ),
        );
        break;
      case TravelServiceType.privateSecurity:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const PrivateSecurityListScreen(),
          ),
        );
        break;
      case TravelServiceType.hotel:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const HotelListScreenEnhanced(),
          ),
        );
        break;
      case TravelServiceType.restaurant:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const RestaurantListScreen(),
          ),
        );
        break;
      case TravelServiceType.flight:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const FlightListScreen(),
          ),
        );
        break;
      case TravelServiceType.cruise:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const CruiseListScreen(),
          ),
        );
        break;
      case TravelServiceType.insurance:
        Navigator.pushNamed(context, '/travel/insurance');
        break;
      case TravelServiceType.visa:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const VisaRequirementsScreen(),
          ),
        );
        break;
    }
  }

  void _navigateToLoyaltyProgramDetails(LoyaltyProgram loyaltyProgram) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            LoyaltyProgramScreen(loyaltyProgram: loyaltyProgram),
      ),
    );
  }

  void _navigateToInsurance() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const InsuranceHomeScreen(),
      ),
    );
  }
}
